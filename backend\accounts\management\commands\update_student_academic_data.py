from django.core.management.base import BaseCommand
from django.db import models
from accounts.models import StudentProfile
from faker import Faker
import random

fake = Faker()

class Command(BaseCommand):
    help = 'Update existing student profiles with Class X and Class XII academic data'

    def handle(self, *args, **options):
        # Get all students that don't have academic data (check for empty string or null)
        students = StudentProfile.objects.filter(
            models.Q(tenth_cgpa__isnull=True) | models.Q(tenth_cgpa='')
        )
        
        if not students.exists():
            self.stdout.write(self.style.SUCCESS('All students already have academic data'))
            return
        
        self.stdout.write(f'Updating {students.count()} students with academic data...')
        
        # Define boards and specializations
        boards = ['CBSE', 'ICSE', 'State Board', 'IB', 'IGCSE']
        twelfth_specializations = ['Science', 'Commerce', 'Arts', 'Humanities']
        
        updated_count = 0
        
        for student in students:
            try:
                # Generate 10th academic details
                tenth_board = random.choice(boards)
                tenth_school = f"{fake.last_name()} {random.choice(['Public School', 'High School', 'International School'])}"
                tenth_year_of_passing = str(int(student.joining_year or 2020) - 4)  # 4 years before joining college
                tenth_cgpa = str(round(random.uniform(7.0, 10.0), 1))
                tenth_percentage = str(round(float(tenth_cgpa) * 9.5, 2))  # Approximation
                
                # Generate 12th academic details
                twelfth_board = random.choice(boards)
                twelfth_school = f"{fake.last_name()} {random.choice(['Public School', 'High School', 'International School'])}"
                twelfth_year_of_passing = str(int(student.joining_year or 2020) - 2)  # 2 years before joining college
                twelfth_cgpa = str(round(random.uniform(7.0, 10.0), 1))
                twelfth_percentage = str(round(float(twelfth_cgpa) * 9.5, 2))  # Approximation
                twelfth_specialization = random.choice(twelfth_specializations)
                
                # Update the student
                student.tenth_cgpa = tenth_cgpa
                student.tenth_percentage = tenth_percentage
                student.tenth_board = tenth_board
                student.tenth_school = tenth_school
                student.tenth_year_of_passing = tenth_year_of_passing
                
                student.twelfth_cgpa = twelfth_cgpa
                student.twelfth_percentage = twelfth_percentage
                student.twelfth_board = twelfth_board
                student.twelfth_school = twelfth_school
                student.twelfth_year_of_passing = twelfth_year_of_passing
                student.twelfth_specialization = twelfth_specialization
                
                student.save()
                updated_count += 1
                
                if updated_count % 50 == 0:
                    self.stdout.write(f'Updated {updated_count} students...')
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error updating student {student.id}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully updated {updated_count} students with academic data')
        )
