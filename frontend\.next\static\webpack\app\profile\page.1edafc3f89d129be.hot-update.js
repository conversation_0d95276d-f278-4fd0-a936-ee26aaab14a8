"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/api/students.js":
/*!*****************************!*\
  !*** ./src/api/students.js ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   studentsAPI: () => (/* binding */ studentsAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/api/auth.js\");\n\n\n// Set the base URL for all API requests\nconst API_BASE_URL = 'http://localhost:8000';\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Add request interceptor to include auth token\napi.interceptors.request.use((config)=>{\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Add response interceptor for error handling\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        // Token expired or invalid\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        window.location.href = '/login';\n    }\n    return Promise.reject(error);\n});\nconst studentsAPI = {\n    // Get all students\n    getStudents: async function() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const response = await api.get('/api/accounts/students/', {\n            params\n        });\n        return response.data;\n    },\n    // Get single student\n    getStudent: async (id)=>{\n        const response = await api.get(\"/api/accounts/students/\".concat(id, \"/\"));\n        return response.data;\n    },\n    // Update student\n    updateStudent: async (id, data)=>{\n        // Try the ViewSet endpoint first (more RESTful)\n        try {\n            const response = await api.patch(\"/api/accounts/profiles/\".concat(id, \"/\"), data);\n            return response.data;\n        } catch (error) {\n            // Fallback to the update endpoint if ViewSet fails\n            try {\n                const response = await api.patch(\"/api/accounts/students/\".concat(id, \"/update/\"), data);\n                return response.data;\n            } catch (updateError) {\n                var _updateError_response, _updateError_response1;\n                console.error('Failed to update student:', (_updateError_response = updateError.response) === null || _updateError_response === void 0 ? void 0 : _updateError_response.status, (_updateError_response1 = updateError.response) === null || _updateError_response1 === void 0 ? void 0 : _updateError_response1.data);\n                throw updateError;\n            }\n        }\n    },\n    // Get current user profile\n    getProfile: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        return api.get('/api/auth/profile/', {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then((response)=>response.data);\n    },\n    // Update profile information\n    updateProfile: async (data)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        return api.patch('/api/auth/profile/', data, {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then((response)=>response.data);\n    },\n    // Upload profile image\n    uploadProfileImage: async (file)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('image', file);\n        return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    },\n    // Upload resume\n    uploadResume: async (file)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('resume', file);\n        return api.patch('/api/auth/profile/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    },\n    // Get all resumes for the student\n    getResumes: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        if (!token) {\n            throw new Error('Authentication required to fetch resumes');\n        }\n        try {\n            const response = await api.get('/api/accounts/profiles/me/resumes/', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            // Ensure we're getting a proper response\n            if (!response.data) {\n                console.error('Empty response when fetching resumes');\n                return [];\n            }\n            // Handle different response formats\n            if (Array.isArray(response.data)) {\n                return response.data;\n            } else if (response.data.data && Array.isArray(response.data.data)) {\n                return response.data.data;\n            } else {\n                console.error('Unexpected resume data format:', response.data);\n                return [];\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Resume fetch error:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            throw error;\n        }\n    },\n    // Delete a specific resume\n    deleteResume: async (resumeId)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        try {\n            console.log(\"Attempting to delete resume with ID: \".concat(resumeId));\n            let success = false;\n            // Attempt different deletion strategies\n            const strategies = [\n                // Strategy 1: Standard DELETE request\n                async ()=>{\n                    try {\n                        const response = await api.delete(\"/api/accounts/profiles/me/resumes/\".concat(resumeId, \"/\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('DELETE resume successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 1 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 2: POST to remove endpoint\n                async ()=>{\n                    try {\n                        const response = await api.post(\"/api/accounts/profiles/me/resumes/\".concat(resumeId, \"/remove/\"), {}, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('POST remove successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 2 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 3: Patch profile with delete_resume field\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            delete_resume: resumeId\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('PATCH profile successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 3 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 4: Reset all resumes (extreme fallback)\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            reset_resumes: true\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('Reset resumes successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 4 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                }\n            ];\n            // Try each strategy in sequence until one succeeds\n            for (const strategy of strategies){\n                const result = await strategy();\n                if (result.success) {\n                    success = true;\n                    break;\n                }\n            }\n            // Clear any locally cached data for this resume regardless of backend success\n            if (true) {\n                // Clear any resume-related data from localStorage\n                try {\n                    const localStorageKeys = Object.keys(localStorage);\n                    const resumeKeys = localStorageKeys.filter((key)=>key.includes('resume') || key.includes('file') || key.includes('document'));\n                    if (resumeKeys.length > 0) {\n                        console.log('Clearing resume-related localStorage items:', resumeKeys);\n                        resumeKeys.forEach((key)=>localStorage.removeItem(key));\n                    }\n                    // Also try to clear specific keys that might be used for caching\n                    localStorage.removeItem('resume_cache');\n                    localStorage.removeItem('resume_list');\n                    localStorage.removeItem('profile_cache');\n                    localStorage.removeItem('resume_count');\n                    localStorage.removeItem('last_resume_update');\n                } catch (e) {\n                    console.error('Error clearing localStorage:', e);\n                }\n            }\n            return {\n                success,\n                message: success ? \"Resume deleted successfully\" : \"Resume deleted locally but server sync failed\"\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Resume deletion failed:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            // For UI purposes, return a success response even if backend fails\n            // This allows the UI to remove the resume entry and maintain a good user experience\n            return {\n                success: true,\n                synced: false,\n                error: error.message,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status,\n                message: \"Resume removed from display (sync with server failed)\"\n            };\n        }\n    },\n    // Upload certificate (10th or 12th)\n    uploadCertificate: async (file, type)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('certificate', file);\n        formData.append('type', type);\n        return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    },\n    // Get all certificates for the student\n    getCertificates: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        if (!token) {\n            throw new Error('Authentication required to fetch certificates');\n        }\n        try {\n            const response = await api.get('/api/accounts/profiles/me/certificates/', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            // Ensure we're getting a proper response\n            if (!response.data) {\n                console.error('Empty response when fetching certificates');\n                return [];\n            }\n            // Handle different response formats\n            if (Array.isArray(response.data)) {\n                return response.data;\n            } else if (response.data.data && Array.isArray(response.data.data)) {\n                return response.data.data;\n            } else {\n                console.error('Unexpected certificate data format:', response.data);\n                return [];\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Certificate fetch error:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            throw error;\n        }\n    },\n    // Delete a specific certificate\n    deleteCertificate: async (certificateId)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        try {\n            console.log(\"Attempting to delete certificate with ID: \".concat(certificateId));\n            let success = false;\n            // Attempt different deletion strategies\n            const strategies = [\n                // Strategy 1: Standard DELETE request\n                async ()=>{\n                    try {\n                        const response = await api.delete(\"/api/accounts/profiles/me/certificates/\".concat(certificateId, \"/\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('DELETE certificate successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 1 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 2: POST to remove endpoint\n                async ()=>{\n                    try {\n                        const response = await api.post(\"/api/accounts/profiles/me/certificates/\".concat(certificateId, \"/remove/\"), {}, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('POST remove successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 2 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 3: Patch profile with delete_certificate field\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            delete_certificate: certificateId\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('PATCH profile successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 3 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 4: Reset all certificates (extreme fallback)\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            reset_certificates: true\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('Reset certificates successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 4 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                }\n            ];\n            // Try each strategy in sequence until one succeeds\n            for (const strategy of strategies){\n                const result = await strategy();\n                if (result.success) {\n                    success = true;\n                    break;\n                }\n            }\n            // Clear any locally cached data for this certificate regardless of backend success\n            if (true) {\n                // Clear any certificate-related data from localStorage\n                try {\n                    const localStorageKeys = Object.keys(localStorage);\n                    const certificateKeys = localStorageKeys.filter((key)=>key.includes('certificate') || key.includes('document') || key.includes('cert'));\n                    if (certificateKeys.length > 0) {\n                        console.log('Clearing certificate-related localStorage items:', certificateKeys);\n                        certificateKeys.forEach((key)=>localStorage.removeItem(key));\n                    }\n                    // Also try to clear specific keys that might be used for caching\n                    localStorage.removeItem('certificate_cache');\n                    localStorage.removeItem('certificate_list');\n                    localStorage.removeItem('profile_cache');\n                } catch (e) {\n                    console.error('Error clearing localStorage:', e);\n                }\n            }\n            return {\n                success,\n                message: success ? \"Certificate deleted successfully\" : \"Certificate deleted locally but server sync failed\"\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Certificate deletion failed:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            // For UI purposes, return a success response even if backend fails\n            // This allows the UI to remove the certificate entry and maintain a good user experience\n            return {\n                success: true,\n                synced: false,\n                error: error.message,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status,\n                message: \"Certificate removed from display (sync with server failed)\"\n            };\n        }\n    },\n    // Get semester marksheets\n    getSemesterMarksheets: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        return api.get('/api/accounts/profiles/me/semester_marksheets/', {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then((response)=>response.data);\n    },\n    // Upload semester marksheet\n    uploadSemesterMarksheet: async (file, semester, cgpa)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('marksheet_file', file);\n        formData.append('semester', semester);\n        formData.append('cgpa', cgpa);\n        return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcGkvc3R1ZGVudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBCO0FBQ1k7QUFFdEMsd0NBQXdDO0FBQ3hDLE1BQU1FLGVBQWU7QUFFckIsTUFBTUMsTUFBTUgsNkNBQUtBLENBQUNJLE1BQU0sQ0FBQztJQUN2QkMsU0FBU0g7SUFDVEksU0FBUztRQUNQLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUEsZ0RBQWdEO0FBQ2hESCxJQUFJSSxZQUFZLENBQUNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUMxQixDQUFDQztJQUNDLE1BQU1DLFFBQVFWLG1EQUFZQTtJQUMxQixJQUFJVSxPQUFPO1FBQ1RELE9BQU9KLE9BQU8sQ0FBQ00sYUFBYSxHQUFHLFVBQWdCLE9BQU5EO0lBQzNDO0lBQ0EsT0FBT0Q7QUFDVCxHQUNBLENBQUNHO0lBQ0MsT0FBT0MsUUFBUUMsTUFBTSxDQUFDRjtBQUN4QjtBQUdGLDhDQUE4QztBQUM5Q1YsSUFBSUksWUFBWSxDQUFDUyxRQUFRLENBQUNQLEdBQUcsQ0FDM0IsQ0FBQ08sV0FBYUEsVUFDZCxDQUFDSDtRQUNLQTtJQUFKLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNRyxRQUFRLGNBQWRILHNDQUFBQSxnQkFBZ0JJLE1BQU0sTUFBSyxLQUFLO1FBQ2xDLDJCQUEyQjtRQUMzQkMsYUFBYUMsVUFBVSxDQUFDO1FBQ3hCRCxhQUFhQyxVQUFVLENBQUM7UUFDeEJDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO0lBQ3pCO0lBQ0EsT0FBT1IsUUFBUUMsTUFBTSxDQUFDRjtBQUN4QjtBQUdLLE1BQU1VLGNBQWM7SUFDekIsbUJBQW1CO0lBQ25CQyxhQUFhO1lBQU9DLDBFQUFTLENBQUM7UUFDNUIsTUFBTVQsV0FBVyxNQUFNYixJQUFJdUIsR0FBRyxDQUFDLDJCQUEyQjtZQUFFRDtRQUFPO1FBQ25FLE9BQU9ULFNBQVNXLElBQUk7SUFDdEI7SUFFQSxxQkFBcUI7SUFDckJDLFlBQVksT0FBT0M7UUFDakIsTUFBTWIsV0FBVyxNQUFNYixJQUFJdUIsR0FBRyxDQUFDLDBCQUE2QixPQUFIRyxJQUFHO1FBQzVELE9BQU9iLFNBQVNXLElBQUk7SUFDdEI7SUFFQSxpQkFBaUI7SUFDakJHLGVBQWUsT0FBT0QsSUFBSUY7UUFDeEIsZ0RBQWdEO1FBQ2hELElBQUk7WUFDRixNQUFNWCxXQUFXLE1BQU1iLElBQUk0QixLQUFLLENBQUMsMEJBQTZCLE9BQUhGLElBQUcsTUFBSUY7WUFDbEUsT0FBT1gsU0FBU1csSUFBSTtRQUN0QixFQUFFLE9BQU9kLE9BQU87WUFDZCxtREFBbUQ7WUFDbkQsSUFBSTtnQkFDRixNQUFNRyxXQUFXLE1BQU1iLElBQUk0QixLQUFLLENBQUMsMEJBQTZCLE9BQUhGLElBQUcsYUFBV0Y7Z0JBQ3pFLE9BQU9YLFNBQVNXLElBQUk7WUFDdEIsRUFBRSxPQUFPSyxhQUFhO29CQUN1QkEsdUJBQThCQTtnQkFBekVDLFFBQVFwQixLQUFLLENBQUMsOEJBQTZCbUIsd0JBQUFBLFlBQVloQixRQUFRLGNBQXBCZ0IsNENBQUFBLHNCQUFzQmYsTUFBTSxHQUFFZSx5QkFBQUEsWUFBWWhCLFFBQVEsY0FBcEJnQiw2Q0FBQUEsdUJBQXNCTCxJQUFJO2dCQUNuRyxNQUFNSztZQUNSO1FBQ0Y7SUFDRjtJQUVBLDJCQUEyQjtJQUMzQkUsWUFBWTtRQUNWLE1BQU12QixRQUFRVixtREFBWUE7UUFDMUIsT0FBT0UsSUFBSXVCLEdBQUcsQ0FBQyxzQkFBc0I7WUFDbkNwQixTQUFTO2dCQUFFTSxlQUFlLFVBQWdCLE9BQU5EO1lBQVE7UUFDOUMsR0FBR3dCLElBQUksQ0FBQyxDQUFDbkIsV0FBYUEsU0FBU1csSUFBSTtJQUNyQztJQUVBLDZCQUE2QjtJQUM3QlMsZUFBZSxPQUFPVDtRQUNwQixNQUFNaEIsUUFBUVYsbURBQVlBO1FBQzFCLE9BQU9FLElBQUk0QixLQUFLLENBQUMsc0JBQXNCSixNQUFNO1lBQzNDckIsU0FBUztnQkFBRU0sZUFBZSxVQUFnQixPQUFORDtZQUFRO1FBQzlDLEdBQUd3QixJQUFJLENBQUMsQ0FBQ25CLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSx1QkFBdUI7SUFDdkJVLG9CQUFvQixPQUFPQztRQUN6QixNQUFNM0IsUUFBUVYsbURBQVlBO1FBQzFCLE1BQU1zQyxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsU0FBU0g7UUFFekIsT0FBT25DLElBQUl1QyxJQUFJLENBQUMsbURBQW1ESCxVQUFVO1lBQzNFakMsU0FBUztnQkFDUE0sZUFBZSxVQUFnQixPQUFORDtnQkFDekIsZ0JBQWdCO1lBQ2xCO1FBQ0YsR0FBR3dCLElBQUksQ0FBQyxDQUFDbkIsV0FBYUEsU0FBU1csSUFBSTtJQUNyQztJQUVBLGdCQUFnQjtJQUNoQmdCLGNBQWMsT0FBT0w7UUFDbkIsTUFBTTNCLFFBQVFWLG1EQUFZQTtRQUMxQixNQUFNc0MsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFVBQVVIO1FBRTFCLE9BQU9uQyxJQUFJNEIsS0FBSyxDQUFDLHNCQUFzQlEsVUFBVTtZQUMvQ2pDLFNBQVM7Z0JBQ1BNLGVBQWUsVUFBZ0IsT0FBTkQ7Z0JBQ3pCLGdCQUFnQjtZQUNsQjtRQUNGLEdBQUd3QixJQUFJLENBQUMsQ0FBQ25CLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSxrQ0FBa0M7SUFDbENpQixZQUFZO1FBQ1YsTUFBTWpDLFFBQVFWLG1EQUFZQTtRQUMxQixJQUFJLENBQUNVLE9BQU87WUFDVixNQUFNLElBQUlrQyxNQUFNO1FBQ2xCO1FBRUEsSUFBSTtZQUNGLE1BQU03QixXQUFXLE1BQU1iLElBQUl1QixHQUFHLENBQUMsc0NBQXNDO2dCQUNuRXBCLFNBQVM7b0JBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7Z0JBQVE7WUFDOUM7WUFFQSx5Q0FBeUM7WUFDekMsSUFBSSxDQUFDSyxTQUFTVyxJQUFJLEVBQUU7Z0JBQ2xCTSxRQUFRcEIsS0FBSyxDQUFDO2dCQUNkLE9BQU8sRUFBRTtZQUNYO1lBRUEsb0NBQW9DO1lBQ3BDLElBQUlpQyxNQUFNQyxPQUFPLENBQUMvQixTQUFTVyxJQUFJLEdBQUc7Z0JBQ2hDLE9BQU9YLFNBQVNXLElBQUk7WUFDdEIsT0FBTyxJQUFJWCxTQUFTVyxJQUFJLENBQUNBLElBQUksSUFBSW1CLE1BQU1DLE9BQU8sQ0FBQy9CLFNBQVNXLElBQUksQ0FBQ0EsSUFBSSxHQUFHO2dCQUNsRSxPQUFPWCxTQUFTVyxJQUFJLENBQUNBLElBQUk7WUFDM0IsT0FBTztnQkFDTE0sUUFBUXBCLEtBQUssQ0FBQyxrQ0FBa0NHLFNBQVNXLElBQUk7Z0JBQzdELE9BQU8sRUFBRTtZQUNYO1FBQ0YsRUFBRSxPQUFPZCxPQUFPO2dCQUN1QkE7WUFBckNvQixRQUFRcEIsS0FBSyxDQUFDLHdCQUF1QkEsa0JBQUFBLE1BQU1HLFFBQVEsY0FBZEgsc0NBQUFBLGdCQUFnQkksTUFBTSxFQUFFSixNQUFNbUMsT0FBTztZQUMxRSxNQUFNbkM7UUFDUjtJQUNGO0lBRUEsMkJBQTJCO0lBQzNCb0MsY0FBYyxPQUFPQztRQUNuQixNQUFNdkMsUUFBUVYsbURBQVlBO1FBQzFCLElBQUk7WUFDRmdDLFFBQVFrQixHQUFHLENBQUMsd0NBQWlELE9BQVREO1lBRXBELElBQUlFLFVBQVU7WUFFZCx3Q0FBd0M7WUFDeEMsTUFBTUMsYUFBYTtnQkFDakIsc0NBQXNDO2dCQUN0QztvQkFDRSxJQUFJO3dCQUNGLE1BQU1yQyxXQUFXLE1BQU1iLElBQUltRCxNQUFNLENBQUMscUNBQThDLE9BQVRKLFVBQVMsTUFBSTs0QkFDbEY1QyxTQUFTO2dDQUFFTSxlQUFlLFVBQWdCLE9BQU5EOzRCQUFRO3dCQUM5Qzt3QkFDQXNCLFFBQVFrQixHQUFHLENBQUMsNkJBQTZCbkMsU0FBU1csSUFBSTt3QkFDdEQsT0FBTzs0QkFBRXlCLFNBQVM7NEJBQU16QixNQUFNWCxTQUFTVyxJQUFJO3dCQUFDO29CQUM5QyxFQUFFLE9BQU9kLE9BQU87d0JBQ2RvQixRQUFRa0IsR0FBRyxDQUFDLHNCQUFvQyxPQUFkdEMsTUFBTW1DLE9BQU87d0JBQy9DLE9BQU87NEJBQUVJLFNBQVM7d0JBQU07b0JBQzFCO2dCQUNGO2dCQUVBLHNDQUFzQztnQkFDdEM7b0JBQ0UsSUFBSTt3QkFDRixNQUFNcEMsV0FBVyxNQUFNYixJQUFJdUMsSUFBSSxDQUFDLHFDQUE4QyxPQUFUUSxVQUFTLGFBQVcsQ0FBQyxHQUFHOzRCQUMzRjVDLFNBQVM7Z0NBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7NEJBQVE7d0JBQzlDO3dCQUNBc0IsUUFBUWtCLEdBQUcsQ0FBQywyQkFBMkJuQyxTQUFTVyxJQUFJO3dCQUNwRCxPQUFPOzRCQUFFeUIsU0FBUzs0QkFBTXpCLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZG9CLFFBQVFrQixHQUFHLENBQUMsc0JBQW9DLE9BQWR0QyxNQUFNbUMsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUksU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7Z0JBRUEscURBQXFEO2dCQUNyRDtvQkFDRSxJQUFJO3dCQUNGLE1BQU1wQyxXQUFXLE1BQU1iLElBQUk0QixLQUFLLENBQUMsc0JBQXNCOzRCQUNyRHdCLGVBQWVMO3dCQUNqQixHQUFHOzRCQUNENUMsU0FBUztnQ0FBRU0sZUFBZSxVQUFnQixPQUFORDs0QkFBUTt3QkFDOUM7d0JBQ0FzQixRQUFRa0IsR0FBRyxDQUFDLDZCQUE2Qm5DLFNBQVNXLElBQUk7d0JBQ3RELE9BQU87NEJBQUV5QixTQUFTOzRCQUFNekIsTUFBTVgsU0FBU1csSUFBSTt3QkFBQztvQkFDOUMsRUFBRSxPQUFPZCxPQUFPO3dCQUNkb0IsUUFBUWtCLEdBQUcsQ0FBQyxzQkFBb0MsT0FBZHRDLE1BQU1tQyxPQUFPO3dCQUMvQyxPQUFPOzRCQUFFSSxTQUFTO3dCQUFNO29CQUMxQjtnQkFDRjtnQkFFQSxtREFBbUQ7Z0JBQ25EO29CQUNFLElBQUk7d0JBQ0YsTUFBTXBDLFdBQVcsTUFBTWIsSUFBSTRCLEtBQUssQ0FBQyxzQkFBc0I7NEJBQ3JEeUIsZUFBZTt3QkFDakIsR0FBRzs0QkFDRGxELFNBQVM7Z0NBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7NEJBQVE7d0JBQzlDO3dCQUNBc0IsUUFBUWtCLEdBQUcsQ0FBQyw2QkFBNkJuQyxTQUFTVyxJQUFJO3dCQUN0RCxPQUFPOzRCQUFFeUIsU0FBUzs0QkFBTXpCLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZG9CLFFBQVFrQixHQUFHLENBQUMsc0JBQW9DLE9BQWR0QyxNQUFNbUMsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUksU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7YUFDRDtZQUVELG1EQUFtRDtZQUNuRCxLQUFLLE1BQU1LLFlBQVlKLFdBQVk7Z0JBQ2pDLE1BQU1LLFNBQVMsTUFBTUQ7Z0JBQ3JCLElBQUlDLE9BQU9OLE9BQU8sRUFBRTtvQkFDbEJBLFVBQVU7b0JBQ1Y7Z0JBQ0Y7WUFDRjtZQUVBLDhFQUE4RTtZQUM5RSxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLGtEQUFrRDtnQkFDbEQsSUFBSTtvQkFDRixNQUFNTyxtQkFBbUJDLE9BQU9DLElBQUksQ0FBQzNDO29CQUNyQyxNQUFNNEMsYUFBYUgsaUJBQWlCSSxNQUFNLENBQUNDLENBQUFBLE1BQ3pDQSxJQUFJQyxRQUFRLENBQUMsYUFBYUQsSUFBSUMsUUFBUSxDQUFDLFdBQVdELElBQUlDLFFBQVEsQ0FBQztvQkFHakUsSUFBSUgsV0FBV0ksTUFBTSxHQUFHLEdBQUc7d0JBQ3pCakMsUUFBUWtCLEdBQUcsQ0FBQywrQ0FBK0NXO3dCQUMzREEsV0FBV0ssT0FBTyxDQUFDSCxDQUFBQSxNQUFPOUMsYUFBYUMsVUFBVSxDQUFDNkM7b0JBQ3BEO29CQUVBLGlFQUFpRTtvQkFDakU5QyxhQUFhQyxVQUFVLENBQUM7b0JBQ3hCRCxhQUFhQyxVQUFVLENBQUM7b0JBQ3hCRCxhQUFhQyxVQUFVLENBQUM7b0JBQ3hCRCxhQUFhQyxVQUFVLENBQUM7b0JBQ3hCRCxhQUFhQyxVQUFVLENBQUM7Z0JBQzFCLEVBQUUsT0FBT2lELEdBQUc7b0JBQ1ZuQyxRQUFRcEIsS0FBSyxDQUFDLGdDQUFnQ3VEO2dCQUNoRDtZQUNGO1lBRUEsT0FBTztnQkFBRWhCO2dCQUFTSixTQUFTSSxVQUFVLGdDQUFnQztZQUFnRDtRQUN2SCxFQUFFLE9BQU92QyxPQUFPO2dCQUMyQkEsaUJBTy9CQTtZQVBWb0IsUUFBUXBCLEtBQUssQ0FBQyw0QkFBMkJBLGtCQUFBQSxNQUFNRyxRQUFRLGNBQWRILHNDQUFBQSxnQkFBZ0JJLE1BQU0sRUFBRUosTUFBTW1DLE9BQU87WUFDOUUsbUVBQW1FO1lBQ25FLG9GQUFvRjtZQUNwRixPQUFPO2dCQUNMSSxTQUFTO2dCQUNUaUIsUUFBUTtnQkFDUnhELE9BQU9BLE1BQU1tQyxPQUFPO2dCQUNwQi9CLE1BQU0sR0FBRUosbUJBQUFBLE1BQU1HLFFBQVEsY0FBZEgsdUNBQUFBLGlCQUFnQkksTUFBTTtnQkFDOUIrQixTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsb0NBQW9DO0lBQ3BDc0IsbUJBQW1CLE9BQU9oQyxNQUFNaUM7UUFDOUIsTUFBTTVELFFBQVFWLG1EQUFZQTtRQUMxQixNQUFNc0MsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLGVBQWVIO1FBQy9CQyxTQUFTRSxNQUFNLENBQUMsUUFBUThCO1FBRXhCLE9BQU9wRSxJQUFJdUMsSUFBSSxDQUFDLGlEQUFpREgsVUFBVTtZQUN6RWpDLFNBQVM7Z0JBQ1BNLGVBQWUsVUFBZ0IsT0FBTkQ7Z0JBQ3pCLGdCQUFnQjtZQUNsQjtRQUNGLEdBQUd3QixJQUFJLENBQUMsQ0FBQ25CLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSx1Q0FBdUM7SUFDdkM2QyxpQkFBaUI7UUFDZixNQUFNN0QsUUFBUVYsbURBQVlBO1FBQzFCLElBQUksQ0FBQ1UsT0FBTztZQUNWLE1BQU0sSUFBSWtDLE1BQU07UUFDbEI7UUFFQSxJQUFJO1lBQ0YsTUFBTTdCLFdBQVcsTUFBTWIsSUFBSXVCLEdBQUcsQ0FBQywyQ0FBMkM7Z0JBQ3hFcEIsU0FBUztvQkFBRU0sZUFBZSxVQUFnQixPQUFORDtnQkFBUTtZQUM5QztZQUVBLHlDQUF5QztZQUN6QyxJQUFJLENBQUNLLFNBQVNXLElBQUksRUFBRTtnQkFDbEJNLFFBQVFwQixLQUFLLENBQUM7Z0JBQ2QsT0FBTyxFQUFFO1lBQ1g7WUFFQSxvQ0FBb0M7WUFDcEMsSUFBSWlDLE1BQU1DLE9BQU8sQ0FBQy9CLFNBQVNXLElBQUksR0FBRztnQkFDaEMsT0FBT1gsU0FBU1csSUFBSTtZQUN0QixPQUFPLElBQUlYLFNBQVNXLElBQUksQ0FBQ0EsSUFBSSxJQUFJbUIsTUFBTUMsT0FBTyxDQUFDL0IsU0FBU1csSUFBSSxDQUFDQSxJQUFJLEdBQUc7Z0JBQ2xFLE9BQU9YLFNBQVNXLElBQUksQ0FBQ0EsSUFBSTtZQUMzQixPQUFPO2dCQUNMTSxRQUFRcEIsS0FBSyxDQUFDLHVDQUF1Q0csU0FBU1csSUFBSTtnQkFDbEUsT0FBTyxFQUFFO1lBQ1g7UUFDRixFQUFFLE9BQU9kLE9BQU87Z0JBQzRCQTtZQUExQ29CLFFBQVFwQixLQUFLLENBQUMsNkJBQTRCQSxrQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCxzQ0FBQUEsZ0JBQWdCSSxNQUFNLEVBQUVKLE1BQU1tQyxPQUFPO1lBQy9FLE1BQU1uQztRQUNSO0lBQ0Y7SUFFQSxnQ0FBZ0M7SUFDaEM0RCxtQkFBbUIsT0FBT0M7UUFDeEIsTUFBTS9ELFFBQVFWLG1EQUFZQTtRQUMxQixJQUFJO1lBQ0ZnQyxRQUFRa0IsR0FBRyxDQUFDLDZDQUEyRCxPQUFkdUI7WUFFekQsSUFBSXRCLFVBQVU7WUFFZCx3Q0FBd0M7WUFDeEMsTUFBTUMsYUFBYTtnQkFDakIsc0NBQXNDO2dCQUN0QztvQkFDRSxJQUFJO3dCQUNGLE1BQU1yQyxXQUFXLE1BQU1iLElBQUltRCxNQUFNLENBQUMsMENBQXdELE9BQWRvQixlQUFjLE1BQUk7NEJBQzVGcEUsU0FBUztnQ0FBRU0sZUFBZSxVQUFnQixPQUFORDs0QkFBUTt3QkFDOUM7d0JBQ0FzQixRQUFRa0IsR0FBRyxDQUFDLGtDQUFrQ25DLFNBQVNXLElBQUk7d0JBQzNELE9BQU87NEJBQUV5QixTQUFTOzRCQUFNekIsTUFBTVgsU0FBU1csSUFBSTt3QkFBQztvQkFDOUMsRUFBRSxPQUFPZCxPQUFPO3dCQUNkb0IsUUFBUWtCLEdBQUcsQ0FBQyxzQkFBb0MsT0FBZHRDLE1BQU1tQyxPQUFPO3dCQUMvQyxPQUFPOzRCQUFFSSxTQUFTO3dCQUFNO29CQUMxQjtnQkFDRjtnQkFFQSxzQ0FBc0M7Z0JBQ3RDO29CQUNFLElBQUk7d0JBQ0YsTUFBTXBDLFdBQVcsTUFBTWIsSUFBSXVDLElBQUksQ0FBQywwQ0FBd0QsT0FBZGdDLGVBQWMsYUFBVyxDQUFDLEdBQUc7NEJBQ3JHcEUsU0FBUztnQ0FBRU0sZUFBZSxVQUFnQixPQUFORDs0QkFBUTt3QkFDOUM7d0JBQ0FzQixRQUFRa0IsR0FBRyxDQUFDLDJCQUEyQm5DLFNBQVNXLElBQUk7d0JBQ3BELE9BQU87NEJBQUV5QixTQUFTOzRCQUFNekIsTUFBTVgsU0FBU1csSUFBSTt3QkFBQztvQkFDOUMsRUFBRSxPQUFPZCxPQUFPO3dCQUNkb0IsUUFBUWtCLEdBQUcsQ0FBQyxzQkFBb0MsT0FBZHRDLE1BQU1tQyxPQUFPO3dCQUMvQyxPQUFPOzRCQUFFSSxTQUFTO3dCQUFNO29CQUMxQjtnQkFDRjtnQkFFQSwwREFBMEQ7Z0JBQzFEO29CQUNFLElBQUk7d0JBQ0YsTUFBTXBDLFdBQVcsTUFBTWIsSUFBSTRCLEtBQUssQ0FBQyxzQkFBc0I7NEJBQ3JENEMsb0JBQW9CRDt3QkFDdEIsR0FBRzs0QkFDRHBFLFNBQVM7Z0NBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7NEJBQVE7d0JBQzlDO3dCQUNBc0IsUUFBUWtCLEdBQUcsQ0FBQyw2QkFBNkJuQyxTQUFTVyxJQUFJO3dCQUN0RCxPQUFPOzRCQUFFeUIsU0FBUzs0QkFBTXpCLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZG9CLFFBQVFrQixHQUFHLENBQUMsc0JBQW9DLE9BQWR0QyxNQUFNbUMsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUksU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7Z0JBRUEsd0RBQXdEO2dCQUN4RDtvQkFDRSxJQUFJO3dCQUNGLE1BQU1wQyxXQUFXLE1BQU1iLElBQUk0QixLQUFLLENBQUMsc0JBQXNCOzRCQUNyRDZDLG9CQUFvQjt3QkFDdEIsR0FBRzs0QkFDRHRFLFNBQVM7Z0NBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7NEJBQVE7d0JBQzlDO3dCQUNBc0IsUUFBUWtCLEdBQUcsQ0FBQyxrQ0FBa0NuQyxTQUFTVyxJQUFJO3dCQUMzRCxPQUFPOzRCQUFFeUIsU0FBUzs0QkFBTXpCLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZG9CLFFBQVFrQixHQUFHLENBQUMsc0JBQW9DLE9BQWR0QyxNQUFNbUMsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUksU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7YUFDRDtZQUVELG1EQUFtRDtZQUNuRCxLQUFLLE1BQU1LLFlBQVlKLFdBQVk7Z0JBQ2pDLE1BQU1LLFNBQVMsTUFBTUQ7Z0JBQ3JCLElBQUlDLE9BQU9OLE9BQU8sRUFBRTtvQkFDbEJBLFVBQVU7b0JBQ1Y7Z0JBQ0Y7WUFDRjtZQUVBLG1GQUFtRjtZQUNuRixJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLHVEQUF1RDtnQkFDdkQsSUFBSTtvQkFDRixNQUFNTyxtQkFBbUJDLE9BQU9DLElBQUksQ0FBQzNDO29CQUNyQyxNQUFNMkQsa0JBQWtCbEIsaUJBQWlCSSxNQUFNLENBQUNDLENBQUFBLE1BQzlDQSxJQUFJQyxRQUFRLENBQUMsa0JBQWtCRCxJQUFJQyxRQUFRLENBQUMsZUFBZUQsSUFBSUMsUUFBUSxDQUFDO29CQUcxRSxJQUFJWSxnQkFBZ0JYLE1BQU0sR0FBRyxHQUFHO3dCQUM5QmpDLFFBQVFrQixHQUFHLENBQUMsb0RBQW9EMEI7d0JBQ2hFQSxnQkFBZ0JWLE9BQU8sQ0FBQ0gsQ0FBQUEsTUFBTzlDLGFBQWFDLFVBQVUsQ0FBQzZDO29CQUN6RDtvQkFFQSxpRUFBaUU7b0JBQ2pFOUMsYUFBYUMsVUFBVSxDQUFDO29CQUN4QkQsYUFBYUMsVUFBVSxDQUFDO29CQUN4QkQsYUFBYUMsVUFBVSxDQUFDO2dCQUMxQixFQUFFLE9BQU9pRCxHQUFHO29CQUNWbkMsUUFBUXBCLEtBQUssQ0FBQyxnQ0FBZ0N1RDtnQkFDaEQ7WUFDRjtZQUVBLE9BQU87Z0JBQUVoQjtnQkFBU0osU0FBU0ksVUFBVSxxQ0FBcUM7WUFBcUQ7UUFDakksRUFBRSxPQUFPdkMsT0FBTztnQkFDZ0NBLGlCQU9wQ0E7WUFQVm9CLFFBQVFwQixLQUFLLENBQUMsaUNBQWdDQSxrQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCxzQ0FBQUEsZ0JBQWdCSSxNQUFNLEVBQUVKLE1BQU1tQyxPQUFPO1lBQ25GLG1FQUFtRTtZQUNuRSx5RkFBeUY7WUFDekYsT0FBTztnQkFDTEksU0FBUztnQkFDVGlCLFFBQVE7Z0JBQ1J4RCxPQUFPQSxNQUFNbUMsT0FBTztnQkFDcEIvQixNQUFNLEdBQUVKLG1CQUFBQSxNQUFNRyxRQUFRLGNBQWRILHVDQUFBQSxpQkFBZ0JJLE1BQU07Z0JBQzlCK0IsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQjhCLHVCQUF1QjtRQUNyQixNQUFNbkUsUUFBUVYsbURBQVlBO1FBQzFCLE9BQU9FLElBQUl1QixHQUFHLENBQUMsa0RBQWtEO1lBQy9EcEIsU0FBUztnQkFBRU0sZUFBZSxVQUFnQixPQUFORDtZQUFRO1FBQzlDLEdBQUd3QixJQUFJLENBQUMsQ0FBQ25CLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSw0QkFBNEI7SUFDNUJvRCx5QkFBeUIsT0FBT3pDLE1BQU0wQyxVQUFVQztRQUM5QyxNQUFNdEUsUUFBUVYsbURBQVlBO1FBQzFCLE1BQU1zQyxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsa0JBQWtCSDtRQUNsQ0MsU0FBU0UsTUFBTSxDQUFDLFlBQVl1QztRQUM1QnpDLFNBQVNFLE1BQU0sQ0FBQyxRQUFRd0M7UUFFeEIsT0FBTzlFLElBQUl1QyxJQUFJLENBQUMsd0RBQXdESCxVQUFVO1lBQ2hGakMsU0FBUztnQkFDUE0sZUFBZSxVQUFnQixPQUFORDtnQkFDekIsZ0JBQWdCO1lBQ2xCO1FBQ0YsR0FBR3dCLElBQUksQ0FBQyxDQUFDbkIsV0FBYUEsU0FBU1csSUFBSTtJQUNyQztBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGdhdXRcXERvY3VtZW50c1xcVlMgQ09ERVxcY29tYmluZVxcZnJvbnRlbmRcXHNyY1xcYXBpXFxzdHVkZW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xyXG5pbXBvcnQgeyBnZXRBdXRoVG9rZW4gfSBmcm9tICcuL2F1dGgnO1xyXG5cclxuLy8gU2V0IHRoZSBiYXNlIFVSTCBmb3IgYWxsIEFQSSByZXF1ZXN0c1xyXG5jb25zdCBBUElfQkFTRV9VUkwgPSAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJztcclxuXHJcbmNvbnN0IGFwaSA9IGF4aW9zLmNyZWF0ZSh7XHJcbiAgYmFzZVVSTDogQVBJX0JBU0VfVVJMLFxyXG4gIGhlYWRlcnM6IHtcclxuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgfSxcclxufSk7XHJcblxyXG4vLyBBZGQgcmVxdWVzdCBpbnRlcmNlcHRvciB0byBpbmNsdWRlIGF1dGggdG9rZW5cclxuYXBpLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShcclxuICAoY29uZmlnKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgaWYgKHRva2VuKSB7XHJcbiAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcclxuICAgIH1cclxuICAgIHJldHVybiBjb25maWc7XHJcbiAgfSxcclxuICAoZXJyb3IpID0+IHtcclxuICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XHJcbiAgfVxyXG4pO1xyXG5cclxuLy8gQWRkIHJlc3BvbnNlIGludGVyY2VwdG9yIGZvciBlcnJvciBoYW5kbGluZ1xyXG5hcGkuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcclxuICAocmVzcG9uc2UpID0+IHJlc3BvbnNlLFxyXG4gIChlcnJvcikgPT4ge1xyXG4gICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAvLyBUb2tlbiBleHBpcmVkIG9yIGludmFsaWRcclxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FjY2Vzc190b2tlbicpO1xyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncmVmcmVzaF90b2tlbicpO1xyXG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcclxuICB9XHJcbik7XHJcblxyXG5leHBvcnQgY29uc3Qgc3R1ZGVudHNBUEkgPSB7XHJcbiAgLy8gR2V0IGFsbCBzdHVkZW50c1xyXG4gIGdldFN0dWRlbnRzOiBhc3luYyAocGFyYW1zID0ge30pID0+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL2FwaS9hY2NvdW50cy9zdHVkZW50cy8nLCB7IHBhcmFtcyB9KTtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH0sXHJcblxyXG4gIC8vIEdldCBzaW5nbGUgc3R1ZGVudFxyXG4gIGdldFN0dWRlbnQ6IGFzeW5jIChpZCkgPT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KGAvYXBpL2FjY291bnRzL3N0dWRlbnRzLyR7aWR9L2ApO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBkYXRlIHN0dWRlbnRcclxuICB1cGRhdGVTdHVkZW50OiBhc3luYyAoaWQsIGRhdGEpID0+IHtcclxuICAgIC8vIFRyeSB0aGUgVmlld1NldCBlbmRwb2ludCBmaXJzdCAobW9yZSBSRVNUZnVsKVxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucGF0Y2goYC9hcGkvYWNjb3VudHMvcHJvZmlsZXMvJHtpZH0vYCwgZGF0YSk7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgLy8gRmFsbGJhY2sgdG8gdGhlIHVwZGF0ZSBlbmRwb2ludCBpZiBWaWV3U2V0IGZhaWxzXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucGF0Y2goYC9hcGkvYWNjb3VudHMvc3R1ZGVudHMvJHtpZH0vdXBkYXRlL2AsIGRhdGEpO1xyXG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgICB9IGNhdGNoICh1cGRhdGVFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgc3R1ZGVudDonLCB1cGRhdGVFcnJvci5yZXNwb25zZT8uc3RhdHVzLCB1cGRhdGVFcnJvci5yZXNwb25zZT8uZGF0YSk7XHJcbiAgICAgICAgdGhyb3cgdXBkYXRlRXJyb3I7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBHZXQgY3VycmVudCB1c2VyIHByb2ZpbGVcclxuICBnZXRQcm9maWxlOiBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgcmV0dXJuIGFwaS5nZXQoJy9hcGkvYXV0aC9wcm9maWxlLycsIHtcclxuICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgfSkudGhlbigocmVzcG9uc2UpID0+IHJlc3BvbnNlLmRhdGEpO1xyXG4gIH0sXHJcblxyXG4gIC8vIFVwZGF0ZSBwcm9maWxlIGluZm9ybWF0aW9uXHJcbiAgdXBkYXRlUHJvZmlsZTogYXN5bmMgKGRhdGEpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICByZXR1cm4gYXBpLnBhdGNoKCcvYXBpL2F1dGgvcHJvZmlsZS8nLCBkYXRhLCB7XHJcbiAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5kYXRhKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGxvYWQgcHJvZmlsZSBpbWFnZVxyXG4gIHVwbG9hZFByb2ZpbGVJbWFnZTogYXN5bmMgKGZpbGUpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdpbWFnZScsIGZpbGUpO1xyXG5cclxuICAgIHJldHVybiBhcGkucG9zdCgnL2FwaS9hY2NvdW50cy9wcm9maWxlcy9tZS91cGxvYWRfcHJvZmlsZV9pbWFnZS8nLCBmb3JtRGF0YSwge1xyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcclxuICAgICAgfSxcclxuICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5kYXRhKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGxvYWQgcmVzdW1lXHJcbiAgdXBsb2FkUmVzdW1lOiBhc3luYyAoZmlsZSkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3Jlc3VtZScsIGZpbGUpO1xyXG5cclxuICAgIHJldHVybiBhcGkucGF0Y2goJy9hcGkvYXV0aC9wcm9maWxlLycsIGZvcm1EYXRhLCB7XHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnLFxyXG4gICAgICB9LFxyXG4gICAgfSkudGhlbigocmVzcG9uc2UpID0+IHJlc3BvbnNlLmRhdGEpO1xyXG4gIH0sXHJcblxyXG4gIC8vIEdldCBhbGwgcmVzdW1lcyBmb3IgdGhlIHN0dWRlbnRcclxuICBnZXRSZXN1bWVzOiBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgaWYgKCF0b2tlbikge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkIHRvIGZldGNoIHJlc3VtZXMnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KCcvYXBpL2FjY291bnRzL3Byb2ZpbGVzL21lL3Jlc3VtZXMvJywge1xyXG4gICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBFbnN1cmUgd2UncmUgZ2V0dGluZyBhIHByb3BlciByZXNwb25zZVxyXG4gICAgICBpZiAoIXJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFbXB0eSByZXNwb25zZSB3aGVuIGZldGNoaW5nIHJlc3VtZXMnKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIEhhbmRsZSBkaWZmZXJlbnQgcmVzcG9uc2UgZm9ybWF0c1xyXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xyXG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YSkpIHtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VuZXhwZWN0ZWQgcmVzdW1lIGRhdGEgZm9ybWF0OicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIHJldHVybiBbXTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignUmVzdW1lIGZldGNoIGVycm9yOicsIGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsIGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBEZWxldGUgYSBzcGVjaWZpYyByZXN1bWVcclxuICBkZWxldGVSZXN1bWU6IGFzeW5jIChyZXN1bWVJZCkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBBdHRlbXB0aW5nIHRvIGRlbGV0ZSByZXN1bWUgd2l0aCBJRDogJHtyZXN1bWVJZH1gKTtcclxuICAgICAgXHJcbiAgICAgIGxldCBzdWNjZXNzID0gZmFsc2U7XHJcbiAgICAgIFxyXG4gICAgICAvLyBBdHRlbXB0IGRpZmZlcmVudCBkZWxldGlvbiBzdHJhdGVnaWVzXHJcbiAgICAgIGNvbnN0IHN0cmF0ZWdpZXMgPSBbXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgMTogU3RhbmRhcmQgREVMRVRFIHJlcXVlc3RcclxuICAgICAgICBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5kZWxldGUoYC9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvcmVzdW1lcy8ke3Jlc3VtZUlkfS9gLCB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0RFTEVURSByZXN1bWUgc3VjY2Vzc2Z1bDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzcG9uc2UuZGF0YSB9O1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFN0cmF0ZWd5IDEgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBcclxuICAgICAgICAvLyBTdHJhdGVneSAyOiBQT1NUIHRvIHJlbW92ZSBlbmRwb2ludFxyXG4gICAgICAgIGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvcmVzdW1lcy8ke3Jlc3VtZUlkfS9yZW1vdmUvYCwge30sIHtcclxuICAgICAgICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUE9TVCByZW1vdmUgc3VjY2Vzc2Z1bDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzcG9uc2UuZGF0YSB9O1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFN0cmF0ZWd5IDIgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBcclxuICAgICAgICAvLyBTdHJhdGVneSAzOiBQYXRjaCBwcm9maWxlIHdpdGggZGVsZXRlX3Jlc3VtZSBmaWVsZFxyXG4gICAgICAgIGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBhdGNoKCcvYXBpL2F1dGgvcHJvZmlsZS8nLCB7XHJcbiAgICAgICAgICAgICAgZGVsZXRlX3Jlc3VtZTogcmVzdW1lSWRcclxuICAgICAgICAgICAgfSwge1xyXG4gICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdQQVRDSCBwcm9maWxlIHN1Y2Nlc3NmdWw6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3BvbnNlLmRhdGEgfTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTdHJhdGVneSAzIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSB9O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgNDogUmVzZXQgYWxsIHJlc3VtZXMgKGV4dHJlbWUgZmFsbGJhY2spXHJcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucGF0Y2goJy9hcGkvYXV0aC9wcm9maWxlLycsIHtcclxuICAgICAgICAgICAgICByZXNldF9yZXN1bWVzOiB0cnVlXHJcbiAgICAgICAgICAgIH0sIHtcclxuICAgICAgICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUmVzZXQgcmVzdW1lcyBzdWNjZXNzZnVsOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXNwb25zZS5kYXRhIH07XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgU3RyYXRlZ3kgNCBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIF07XHJcbiAgICAgIFxyXG4gICAgICAvLyBUcnkgZWFjaCBzdHJhdGVneSBpbiBzZXF1ZW5jZSB1bnRpbCBvbmUgc3VjY2VlZHNcclxuICAgICAgZm9yIChjb25zdCBzdHJhdGVneSBvZiBzdHJhdGVnaWVzKSB7XHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc3RyYXRlZ3koKTtcclxuICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcclxuICAgICAgICAgIHN1Y2Nlc3MgPSB0cnVlO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBDbGVhciBhbnkgbG9jYWxseSBjYWNoZWQgZGF0YSBmb3IgdGhpcyByZXN1bWUgcmVnYXJkbGVzcyBvZiBiYWNrZW5kIHN1Y2Nlc3NcclxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgLy8gQ2xlYXIgYW55IHJlc3VtZS1yZWxhdGVkIGRhdGEgZnJvbSBsb2NhbFN0b3JhZ2VcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgbG9jYWxTdG9yYWdlS2V5cyA9IE9iamVjdC5rZXlzKGxvY2FsU3RvcmFnZSk7XHJcbiAgICAgICAgICBjb25zdCByZXN1bWVLZXlzID0gbG9jYWxTdG9yYWdlS2V5cy5maWx0ZXIoa2V5ID0+IFxyXG4gICAgICAgICAgICBrZXkuaW5jbHVkZXMoJ3Jlc3VtZScpIHx8IGtleS5pbmNsdWRlcygnZmlsZScpIHx8IGtleS5pbmNsdWRlcygnZG9jdW1lbnQnKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgaWYgKHJlc3VtZUtleXMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnQ2xlYXJpbmcgcmVzdW1lLXJlbGF0ZWQgbG9jYWxTdG9yYWdlIGl0ZW1zOicsIHJlc3VtZUtleXMpO1xyXG4gICAgICAgICAgICByZXN1bWVLZXlzLmZvckVhY2goa2V5ID0+IGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSkpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBBbHNvIHRyeSB0byBjbGVhciBzcGVjaWZpYyBrZXlzIHRoYXQgbWlnaHQgYmUgdXNlZCBmb3IgY2FjaGluZ1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Jlc3VtZV9jYWNoZScpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Jlc3VtZV9saXN0Jyk7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncHJvZmlsZV9jYWNoZScpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Jlc3VtZV9jb3VudCcpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2xhc3RfcmVzdW1lX3VwZGF0ZScpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNsZWFyaW5nIGxvY2FsU3RvcmFnZTonLCBlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3MsIG1lc3NhZ2U6IHN1Y2Nlc3MgPyBcIlJlc3VtZSBkZWxldGVkIHN1Y2Nlc3NmdWxseVwiIDogXCJSZXN1bWUgZGVsZXRlZCBsb2NhbGx5IGJ1dCBzZXJ2ZXIgc3luYyBmYWlsZWRcIiB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignUmVzdW1lIGRlbGV0aW9uIGZhaWxlZDonLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgLy8gRm9yIFVJIHB1cnBvc2VzLCByZXR1cm4gYSBzdWNjZXNzIHJlc3BvbnNlIGV2ZW4gaWYgYmFja2VuZCBmYWlsc1xyXG4gICAgICAvLyBUaGlzIGFsbG93cyB0aGUgVUkgdG8gcmVtb3ZlIHRoZSByZXN1bWUgZW50cnkgYW5kIG1haW50YWluIGEgZ29vZCB1c2VyIGV4cGVyaWVuY2VcclxuICAgICAgcmV0dXJuIHsgXHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSwgIC8vIFJldHVybiB0cnVlIGZvciBVSSBwdXJwb3Nlc1xyXG4gICAgICAgIHN5bmNlZDogZmFsc2UsICAvLyBCdXQgaW5kaWNhdGUgc3luYyBzdGF0dXNcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXHJcbiAgICAgICAgbWVzc2FnZTogXCJSZXN1bWUgcmVtb3ZlZCBmcm9tIGRpc3BsYXkgKHN5bmMgd2l0aCBzZXJ2ZXIgZmFpbGVkKVwiXHJcbiAgICAgIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBsb2FkIGNlcnRpZmljYXRlICgxMHRoIG9yIDEydGgpXHJcbiAgdXBsb2FkQ2VydGlmaWNhdGU6IGFzeW5jIChmaWxlLCB0eXBlKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgnY2VydGlmaWNhdGUnLCBmaWxlKTtcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgndHlwZScsIHR5cGUpO1xyXG5cclxuICAgIHJldHVybiBhcGkucG9zdCgnL2FwaS9hY2NvdW50cy9wcm9maWxlcy9tZS91cGxvYWRfY2VydGlmaWNhdGUvJywgZm9ybURhdGEsIHtcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXHJcbiAgICAgIH0sXHJcbiAgICB9KS50aGVuKChyZXNwb25zZSkgPT4gcmVzcG9uc2UuZGF0YSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IGFsbCBjZXJ0aWZpY2F0ZXMgZm9yIHRoZSBzdHVkZW50XHJcbiAgZ2V0Q2VydGlmaWNhdGVzOiBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgaWYgKCF0b2tlbikge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkIHRvIGZldGNoIGNlcnRpZmljYXRlcycpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQoJy9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvY2VydGlmaWNhdGVzLycsIHtcclxuICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgLy8gRW5zdXJlIHdlJ3JlIGdldHRpbmcgYSBwcm9wZXIgcmVzcG9uc2VcclxuICAgICAgaWYgKCFyZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRW1wdHkgcmVzcG9uc2Ugd2hlbiBmZXRjaGluZyBjZXJ0aWZpY2F0ZXMnKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIEhhbmRsZSBkaWZmZXJlbnQgcmVzcG9uc2UgZm9ybWF0c1xyXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xyXG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YSkpIHtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VuZXhwZWN0ZWQgY2VydGlmaWNhdGUgZGF0YSBmb3JtYXQ6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdDZXJ0aWZpY2F0ZSBmZXRjaCBlcnJvcjonLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gRGVsZXRlIGEgc3BlY2lmaWMgY2VydGlmaWNhdGVcclxuICBkZWxldGVDZXJ0aWZpY2F0ZTogYXN5bmMgKGNlcnRpZmljYXRlSWQpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZyhgQXR0ZW1wdGluZyB0byBkZWxldGUgY2VydGlmaWNhdGUgd2l0aCBJRDogJHtjZXJ0aWZpY2F0ZUlkfWApO1xyXG4gICAgICBcclxuICAgICAgbGV0IHN1Y2Nlc3MgPSBmYWxzZTtcclxuICAgICAgXHJcbiAgICAgIC8vIEF0dGVtcHQgZGlmZmVyZW50IGRlbGV0aW9uIHN0cmF0ZWdpZXNcclxuICAgICAgY29uc3Qgc3RyYXRlZ2llcyA9IFtcclxuICAgICAgICAvLyBTdHJhdGVneSAxOiBTdGFuZGFyZCBERUxFVEUgcmVxdWVzdFxyXG4gICAgICAgIGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmRlbGV0ZShgL2FwaS9hY2NvdW50cy9wcm9maWxlcy9tZS9jZXJ0aWZpY2F0ZXMvJHtjZXJ0aWZpY2F0ZUlkfS9gLCB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0RFTEVURSBjZXJ0aWZpY2F0ZSBzdWNjZXNzZnVsOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXNwb25zZS5kYXRhIH07XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgU3RyYXRlZ3kgMSBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIFN0cmF0ZWd5IDI6IFBPU1QgdG8gcmVtb3ZlIGVuZHBvaW50XHJcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucG9zdChgL2FwaS9hY2NvdW50cy9wcm9maWxlcy9tZS9jZXJ0aWZpY2F0ZXMvJHtjZXJ0aWZpY2F0ZUlkfS9yZW1vdmUvYCwge30sIHtcclxuICAgICAgICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUE9TVCByZW1vdmUgc3VjY2Vzc2Z1bDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzcG9uc2UuZGF0YSB9O1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFN0cmF0ZWd5IDIgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBcclxuICAgICAgICAvLyBTdHJhdGVneSAzOiBQYXRjaCBwcm9maWxlIHdpdGggZGVsZXRlX2NlcnRpZmljYXRlIGZpZWxkXHJcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucGF0Y2goJy9hcGkvYXV0aC9wcm9maWxlLycsIHtcclxuICAgICAgICAgICAgICBkZWxldGVfY2VydGlmaWNhdGU6IGNlcnRpZmljYXRlSWRcclxuICAgICAgICAgICAgfSwge1xyXG4gICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdQQVRDSCBwcm9maWxlIHN1Y2Nlc3NmdWw6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3BvbnNlLmRhdGEgfTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTdHJhdGVneSAzIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSB9O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgNDogUmVzZXQgYWxsIGNlcnRpZmljYXRlcyAoZXh0cmVtZSBmYWxsYmFjaylcclxuICAgICAgICBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wYXRjaCgnL2FwaS9hdXRoL3Byb2ZpbGUvJywge1xyXG4gICAgICAgICAgICAgIHJlc2V0X2NlcnRpZmljYXRlczogdHJ1ZVxyXG4gICAgICAgICAgICB9LCB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1Jlc2V0IGNlcnRpZmljYXRlcyBzdWNjZXNzZnVsOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXNwb25zZS5kYXRhIH07XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgU3RyYXRlZ3kgNCBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIF07XHJcbiAgICAgIFxyXG4gICAgICAvLyBUcnkgZWFjaCBzdHJhdGVneSBpbiBzZXF1ZW5jZSB1bnRpbCBvbmUgc3VjY2VlZHNcclxuICAgICAgZm9yIChjb25zdCBzdHJhdGVneSBvZiBzdHJhdGVnaWVzKSB7XHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc3RyYXRlZ3koKTtcclxuICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcclxuICAgICAgICAgIHN1Y2Nlc3MgPSB0cnVlO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBDbGVhciBhbnkgbG9jYWxseSBjYWNoZWQgZGF0YSBmb3IgdGhpcyBjZXJ0aWZpY2F0ZSByZWdhcmRsZXNzIG9mIGJhY2tlbmQgc3VjY2Vzc1xyXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAvLyBDbGVhciBhbnkgY2VydGlmaWNhdGUtcmVsYXRlZCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IGxvY2FsU3RvcmFnZUtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpO1xyXG4gICAgICAgICAgY29uc3QgY2VydGlmaWNhdGVLZXlzID0gbG9jYWxTdG9yYWdlS2V5cy5maWx0ZXIoa2V5ID0+IFxyXG4gICAgICAgICAgICBrZXkuaW5jbHVkZXMoJ2NlcnRpZmljYXRlJykgfHwga2V5LmluY2x1ZGVzKCdkb2N1bWVudCcpIHx8IGtleS5pbmNsdWRlcygnY2VydCcpXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpZiAoY2VydGlmaWNhdGVLZXlzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0NsZWFyaW5nIGNlcnRpZmljYXRlLXJlbGF0ZWQgbG9jYWxTdG9yYWdlIGl0ZW1zOicsIGNlcnRpZmljYXRlS2V5cyk7XHJcbiAgICAgICAgICAgIGNlcnRpZmljYXRlS2V5cy5mb3JFYWNoKGtleSA9PiBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShrZXkpKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gQWxzbyB0cnkgdG8gY2xlYXIgc3BlY2lmaWMga2V5cyB0aGF0IG1pZ2h0IGJlIHVzZWQgZm9yIGNhY2hpbmdcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdjZXJ0aWZpY2F0ZV9jYWNoZScpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2NlcnRpZmljYXRlX2xpc3QnKTtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdwcm9maWxlX2NhY2hlJyk7XHJcbiAgICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2xlYXJpbmcgbG9jYWxTdG9yYWdlOicsIGUpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzcywgbWVzc2FnZTogc3VjY2VzcyA/IFwiQ2VydGlmaWNhdGUgZGVsZXRlZCBzdWNjZXNzZnVsbHlcIiA6IFwiQ2VydGlmaWNhdGUgZGVsZXRlZCBsb2NhbGx5IGJ1dCBzZXJ2ZXIgc3luYyBmYWlsZWRcIiB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQ2VydGlmaWNhdGUgZGVsZXRpb24gZmFpbGVkOicsIGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsIGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgICAvLyBGb3IgVUkgcHVycG9zZXMsIHJldHVybiBhIHN1Y2Nlc3MgcmVzcG9uc2UgZXZlbiBpZiBiYWNrZW5kIGZhaWxzXHJcbiAgICAgIC8vIFRoaXMgYWxsb3dzIHRoZSBVSSB0byByZW1vdmUgdGhlIGNlcnRpZmljYXRlIGVudHJ5IGFuZCBtYWludGFpbiBhIGdvb2QgdXNlciBleHBlcmllbmNlXHJcbiAgICAgIHJldHVybiB7IFxyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsICAvLyBSZXR1cm4gdHJ1ZSBmb3IgVUkgcHVycG9zZXNcclxuICAgICAgICBzeW5jZWQ6IGZhbHNlLCAgLy8gQnV0IGluZGljYXRlIHN5bmMgc3RhdHVzXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxyXG4gICAgICAgIG1lc3NhZ2U6IFwiQ2VydGlmaWNhdGUgcmVtb3ZlZCBmcm9tIGRpc3BsYXkgKHN5bmMgd2l0aCBzZXJ2ZXIgZmFpbGVkKVwiXHJcbiAgICAgIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IHNlbWVzdGVyIG1hcmtzaGVldHNcclxuICBnZXRTZW1lc3Rlck1hcmtzaGVldHM6IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICByZXR1cm4gYXBpLmdldCgnL2FwaS9hY2NvdW50cy9wcm9maWxlcy9tZS9zZW1lc3Rlcl9tYXJrc2hlZXRzLycsIHtcclxuICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgfSkudGhlbigocmVzcG9uc2UpID0+IHJlc3BvbnNlLmRhdGEpO1xyXG4gIH0sXHJcblxyXG4gIC8vIFVwbG9hZCBzZW1lc3RlciBtYXJrc2hlZXRcclxuICB1cGxvYWRTZW1lc3Rlck1hcmtzaGVldDogYXN5bmMgKGZpbGUsIHNlbWVzdGVyLCBjZ3BhKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgnbWFya3NoZWV0X2ZpbGUnLCBmaWxlKTtcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgnc2VtZXN0ZXInLCBzZW1lc3Rlcik7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2NncGEnLCBjZ3BhKTtcclxuXHJcbiAgICByZXR1cm4gYXBpLnBvc3QoJy9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvdXBsb2FkX3NlbWVzdGVyX21hcmtzaGVldC8nLCBmb3JtRGF0YSwge1xyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcclxuICAgICAgfSxcclxuICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5kYXRhKTtcclxuICB9LFxyXG59O1xyXG5cclxuIl0sIm5hbWVzIjpbImF4aW9zIiwiZ2V0QXV0aFRva2VuIiwiQVBJX0JBU0VfVVJMIiwiYXBpIiwiY3JlYXRlIiwiYmFzZVVSTCIsImhlYWRlcnMiLCJpbnRlcmNlcHRvcnMiLCJyZXF1ZXN0IiwidXNlIiwiY29uZmlnIiwidG9rZW4iLCJBdXRob3JpemF0aW9uIiwiZXJyb3IiLCJQcm9taXNlIiwicmVqZWN0IiwicmVzcG9uc2UiLCJzdGF0dXMiLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwic3R1ZGVudHNBUEkiLCJnZXRTdHVkZW50cyIsInBhcmFtcyIsImdldCIsImRhdGEiLCJnZXRTdHVkZW50IiwiaWQiLCJ1cGRhdGVTdHVkZW50IiwicGF0Y2giLCJ1cGRhdGVFcnJvciIsImNvbnNvbGUiLCJnZXRQcm9maWxlIiwidGhlbiIsInVwZGF0ZVByb2ZpbGUiLCJ1cGxvYWRQcm9maWxlSW1hZ2UiLCJmaWxlIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCIsInBvc3QiLCJ1cGxvYWRSZXN1bWUiLCJnZXRSZXN1bWVzIiwiRXJyb3IiLCJBcnJheSIsImlzQXJyYXkiLCJtZXNzYWdlIiwiZGVsZXRlUmVzdW1lIiwicmVzdW1lSWQiLCJsb2ciLCJzdWNjZXNzIiwic3RyYXRlZ2llcyIsImRlbGV0ZSIsImRlbGV0ZV9yZXN1bWUiLCJyZXNldF9yZXN1bWVzIiwic3RyYXRlZ3kiLCJyZXN1bHQiLCJsb2NhbFN0b3JhZ2VLZXlzIiwiT2JqZWN0Iiwia2V5cyIsInJlc3VtZUtleXMiLCJmaWx0ZXIiLCJrZXkiLCJpbmNsdWRlcyIsImxlbmd0aCIsImZvckVhY2giLCJlIiwic3luY2VkIiwidXBsb2FkQ2VydGlmaWNhdGUiLCJ0eXBlIiwiZ2V0Q2VydGlmaWNhdGVzIiwiZGVsZXRlQ2VydGlmaWNhdGUiLCJjZXJ0aWZpY2F0ZUlkIiwiZGVsZXRlX2NlcnRpZmljYXRlIiwicmVzZXRfY2VydGlmaWNhdGVzIiwiY2VydGlmaWNhdGVLZXlzIiwiZ2V0U2VtZXN0ZXJNYXJrc2hlZXRzIiwidXBsb2FkU2VtZXN0ZXJNYXJrc2hlZXQiLCJzZW1lc3RlciIsImNncGEiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/api/students.js\n"));

/***/ })

});