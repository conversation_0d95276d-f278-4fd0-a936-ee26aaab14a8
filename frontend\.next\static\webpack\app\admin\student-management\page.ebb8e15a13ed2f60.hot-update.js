"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/auth */ \"(app-pages-browser)/./src/api/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            const studentsData = allData.map((student)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || '',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || '',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data - use actual backend data\n                    semester_cgpas: student.semester_marksheets || [],\n                    semester_marksheets: student.semester_marksheets || []\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            var _editedStudent_name, _editedStudent_name1;\n            setLoading(true);\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information\n                first_name: ((_editedStudent_name = editedStudent.name) === null || _editedStudent_name === void 0 ? void 0 : _editedStudent_name.split(' ')[0]) || '',\n                last_name: ((_editedStudent_name1 = editedStudent.name) === null || _editedStudent_name1 === void 0 ? void 0 : _editedStudent_name1.split(' ').slice(1).join(' ')) || '',\n                student_id: editedStudent.rollNumber,\n                contact_email: editedStudent.email,\n                phone: editedStudent.phone,\n                branch: editedStudent.department,\n                gpa: editedStudent.gpa,\n                // Academic details\n                joining_year: editedStudent.joining_year,\n                passout_year: editedStudent.passout_year,\n                // Personal details\n                date_of_birth: editedStudent.dateOfBirth,\n                address: editedStudent.address,\n                city: editedStudent.city,\n                district: editedStudent.district,\n                state: editedStudent.state,\n                pincode: editedStudent.pincode,\n                country: editedStudent.country,\n                parent_contact: editedStudent.parentContact,\n                education: editedStudent.education,\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.join(', ') : editedStudent.skills,\n                // Academic scores\n                tenth_cgpa: editedStudent.tenth_cgpa,\n                tenth_percentage: editedStudent.tenth_percentage,\n                tenth_board: editedStudent.tenth_board,\n                tenth_school: editedStudent.tenth_school,\n                tenth_year_of_passing: editedStudent.tenth_year_of_passing,\n                tenth_location: editedStudent.tenth_location,\n                tenth_specialization: editedStudent.tenth_specialization,\n                twelfth_cgpa: editedStudent.twelfth_cgpa,\n                twelfth_percentage: editedStudent.twelfth_percentage,\n                twelfth_board: editedStudent.twelfth_board,\n                twelfth_school: editedStudent.twelfth_school,\n                twelfth_year_of_passing: editedStudent.twelfth_year_of_passing,\n                twelfth_location: editedStudent.twelfth_location,\n                twelfth_specialization: editedStudent.twelfth_specialization\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = semesterData.cgpa;\n                    }\n                });\n            }\n            // Make API call to update student\n            const updatedStudent = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, updateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            console.error('Error updating student:', error);\n            alert('Failed to update student profile. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 525,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 526,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 524,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 523,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 558,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 533,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 532,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 575,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 582,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 591,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 612,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 571,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"vV3ywjzqfJaaIG37j/kyv5UkL2w=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});