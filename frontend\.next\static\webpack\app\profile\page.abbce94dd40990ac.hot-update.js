"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/app/profile/page.jsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.jsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaFileAlt,FaMapMarkerAlt,FaPhoneAlt,FaSpinner,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _ResumeModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ResumeModal */ \"(app-pages-browser)/./src/app/profile/ResumeModal.jsx\");\n/* harmony import */ var _DocumentsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentsModal */ \"(app-pages-browser)/./src/app/profile/DocumentsModal.jsx\");\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ProfilePage() {\n    var _profile_user;\n    _s();\n    const [isResumeModalOpen, setIsResumeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [semesterMarksheets, setSemesterMarksheets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [resumeCount, setResumeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lastResumeUpdate, setLastResumeUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companyStats, setCompanyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalListings: 0,\n        eligibleJobs: 0,\n        loading: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfilePage.useEffect\": ()=>{\n            async function fetchProfileData() {\n                try {\n                    setLoading(true);\n                    const profileData = await _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.getProfile();\n                    setProfile(profileData);\n                    // Fetch semester marksheets\n                    const marksheets = await _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.getSemesterMarksheets();\n                    setSemesterMarksheets(marksheets);\n                    // Fetch resume count\n                    await fetchResumeCount();\n                    // Fetch company stats\n                    fetchCompanyStats();\n                    setLoading(false);\n                } catch (err) {\n                    console.error('Error fetching profile:', err);\n                    setError('Failed to load profile data. Please try again later.');\n                    setLoading(false);\n                }\n            }\n            fetchProfileData();\n        }\n    }[\"ProfilePage.useEffect\"], []);\n    // Function to fetch resume count\n    const fetchResumeCount = async ()=>{\n        try {\n            const resumes = await _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.getResumes();\n            setResumeCount(resumes.length);\n            if (resumes.length > 0) {\n                // Get the most recent upload date\n                const mostRecent = resumes.reduce((latest, resume)=>{\n                    const resumeDate = new Date(resume.uploaded_at || resume.created_at);\n                    const latestDate = new Date(latest);\n                    return resumeDate > latestDate ? resume.uploaded_at || resume.created_at : latest;\n                }, resumes[0].uploaded_at || resumes[0].created_at);\n                setLastResumeUpdate(mostRecent);\n            }\n        } catch (err) {\n            console.error('Error fetching resumes:', err);\n            // Fallback to profile resume if available\n            if (profile === null || profile === void 0 ? void 0 : profile.resume) {\n                setResumeCount(1);\n                setLastResumeUpdate(profile.updated_at);\n            }\n        }\n    };\n    // Function to fetch company statistics\n    const fetchCompanyStats = async ()=>{\n        try {\n            setCompanyStats((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            // Import and use API functions from companies.js\n            const { getCompanyStats, fetchCompanies } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_api_companies_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../../api/companies */ \"(app-pages-browser)/./src/api/companies.js\"));\n            // Fetch all companies to get total count\n            const companies = await fetchCompanies();\n            // Get stats if available\n            let stats;\n            try {\n                const statsResponse = await getCompanyStats();\n                stats = statsResponse.data || statsResponse;\n            } catch (error) {\n                console.log('Could not fetch company stats, calculating from companies data');\n                stats = {\n                    total: companies.length,\n                    active_jobs: companies.reduce((sum, company)=>sum + (company.totalActiveJobs || 0), 0)\n                };\n            }\n            // Calculate eligible jobs based on profile criteria (e.g., CGPA requirements)\n            const eligibleJobsCount = calculateEligibleJobs(companies, profile);\n            setCompanyStats({\n                totalListings: stats.total || companies.length,\n                eligibleJobs: eligibleJobsCount,\n                loading: false\n            });\n        } catch (error) {\n            console.error('Error fetching company stats:', error);\n            setCompanyStats({\n                totalListings: 0,\n                eligibleJobs: 0,\n                loading: false\n            });\n        }\n    };\n    // Helper function to calculate eligible jobs based on profile\n    const calculateEligibleJobs = (companies, profile)=>{\n        if (!companies || !profile) return 0;\n        // Get user's CGPA for comparison\n        const userCgpa = parseFloat(getOverallCGPA());\n        // Get user's branch/major for matching\n        const userBranch = profile.branch;\n        // Count jobs that match the user's criteria\n        let eligibleCount = 0;\n        // For each company, check eligibility\n        companies.forEach((company)=>{\n            // In a real implementation, we would check each job's requirements\n            // For now, use a simple heuristic based on company tier\n            const companyJobCount = company.totalActiveJobs || 0;\n            let eligibilityPercent = 0;\n            // Very basic eligibility logic (would be replaced with actual requirements)\n            if (userCgpa >= 8.5) {\n                eligibilityPercent = 0.9; // 90% of jobs eligible for high CGPA students\n            } else if (userCgpa >= 7.5) {\n                eligibilityPercent = 0.75; // 75% eligible for good CGPA students\n            } else if (userCgpa >= 6.5) {\n                eligibilityPercent = 0.5; // 50% eligible for average CGPA students\n            } else {\n                eligibilityPercent = 0.25; // 25% eligible for below average CGPA students\n            }\n            // Add to eligible count\n            eligibleCount += Math.floor(companyJobCount * eligibilityPercent);\n        });\n        return eligibleCount;\n    };\n    // Function to handle profile image upload\n    const handleProfileImageUpload = async (file)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.uploadProfileImage(file);\n            // Refresh profile data\n            const profileData = await _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.getProfile();\n            setProfile(profileData);\n        } catch (err) {\n            console.error('Error uploading profile image:', err);\n            alert('Failed to upload profile image. Please try again.');\n        }\n    };\n    // Function to handle resume upload\n    const handleResumeUpload = async (file)=>{\n        await _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.uploadResume(file);\n        // Refresh resume count after upload\n        await fetchResumeCount();\n    };\n    // Function to handle resume delete\n    const handleResumeDelete = async (resume)=>{\n        try {\n            // Clear any cached resume data\n            if ( true && (resume === null || resume === void 0 ? void 0 : resume.id)) {\n                localStorage.removeItem(\"resume_\".concat(resume.id));\n                localStorage.removeItem('resume_count');\n                localStorage.removeItem('last_resume_update');\n            }\n            // Force refresh resume count\n            await fetchResumeCount();\n            // If we were displaying a specific resume that was deleted, clear it\n            if ((resume === null || resume === void 0 ? void 0 : resume.url) === (profile === null || profile === void 0 ? void 0 : profile.resume)) {\n                const updatedProfile = {\n                    ...profile,\n                    resume: null\n                };\n                setProfile(updatedProfile);\n            }\n        } catch (error) {\n            console.error('Error handling resume deletion:', error);\n        }\n    };\n    // Get overall CGPA from database\n    const getOverallCGPA = ()=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.gpa) || '0.00';\n    };\n    // Calculate percentage from CGPA (approximation)\n    const calculatePercentage = (cgpa)=>{\n        if (!cgpa || cgpa === '-') return '-';\n        return (parseFloat(cgpa) * 9.5).toFixed(2) + '%';\n    };\n    // Get semester CGPA value\n    const getSemesterCGPA = (semNumber)=>{\n        if (!profile) return '-';\n        const semesterCGPA = profile[\"semester\".concat(semNumber, \"_cgpa\")];\n        return semesterCGPA || '-';\n    };\n    // Get semester marksheets sorted by semester number\n    const getSortedSemesterMarksheets = ()=>{\n        if (!semesterMarksheets) return [];\n        return [\n            ...semesterMarksheets\n        ].sort((a, b)=>a.semester - b.semester);\n    };\n    // Format date to display period (e.g., \"Sep 2021 - Aug 2025\")\n    const formatEducationPeriod = (startYear, endYear)=>{\n        if (!startYear || !endYear) return '-';\n        return \"\".concat(startYear, \" - \").concat(endYear);\n    };\n    // Function to display either the profile image or a fallback with initial\n    const renderProfileImage = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-50 h-50 bg-blue-100 flex items-center justify-center rounded-lg mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-2xl\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, this);\n        }\n        if (profile === null || profile === void 0 ? void 0 : profile.profile_image_url) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-50 h-50 bg-blue-100 object-center text-center rounded-lg mb-4 relative mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: profile.profile_image_url,\n                    alt: \"\".concat(profile.first_name, \" \").concat(profile.last_name),\n                    fill: true,\n                    className: \"rounded-lg object-cover\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this);\n        }\n        // Fallback to initial\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4 mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-3xl font-bold\",\n                children: (profile === null || profile === void 0 ? void 0 : profile.initial) || ((profile === null || profile === void 0 ? void 0 : profile.first_name) ? profile.first_name[0].toUpperCase() : 'S')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    };\n    // Display loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-4xl mr-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xl text-gray-700\",\n                    children: \"Loading profile...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, this);\n    }\n    // Display error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 text-xl mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: renderProfileImage()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-center mt-2 text-gray-800\",\n                                children: (profile === null || profile === void 0 ? void 0 : profile.first_name) && (profile === null || profile === void 0 ? void 0 : profile.last_name) ? \"\".concat(profile.first_name, \" \").concat(profile.last_name) : '-'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 space-y-3 text-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Student ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.student_id) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Major\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.branch) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Passed Out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.passout_year) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Gender\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.gender) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Birthday\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.date_of_birth) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.phone) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.contact_email) || (profile === null || profile === void 0 ? void 0 : (_profile_user = profile.user) === null || _profile_user === void 0 ? void 0 : _profile_user.email) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Campus\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.college_name) || '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 w-20\",\n                                                children: \"Placement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: [\n                                                    \": \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.joining_year) && (profile === null || profile === void 0 ? void 0 : profile.passout_year) ? \"\".concat(profile.joining_year, \"-\").concat(profile.passout_year) : '-'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-6 text-gray-800\",\n                                        children: \"Academic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: \"Semester Wise score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 font-medium\",\n                                                                children: calculateOverallCGPA()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 ml-1\",\n                                                                children: \"CGPA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 ml-2\",\n                                                                children: calculatePercentage(calculateOverallCGPA())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: formatEducationPeriod(profile === null || profile === void 0 ? void 0 : profile.joining_year, profile === null || profile === void 0 ? void 0 : profile.passout_year)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full border-collapse border border-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                    children: \"Sem\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                        children: sem\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\",\n                                                                    children: \"Cgpa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-700\",\n                                                                        children: getSemesterCGPA(sem)\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class XII\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_cgpa) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                                        children: \"CGPA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 ml-2\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_percentage) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_year_of_passing) ? \"\".concat(parseInt(profile.twelfth_year_of_passing) - 2, \" - \").concat(profile.twelfth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"College :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class X\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_cgpa) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                                        children: \"CGPA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 ml-2\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_percentage) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_year_of_passing) ? \"\".concat(parseInt(profile.tenth_year_of_passing) - 1, \" - \").concat(profile.tenth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"School :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.tenth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.tenth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"Companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this),\n                                    companyStats.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                className: \"animate-spin text-blue-500 text-xl mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Loading company data...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Total Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.totalListings\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Eligible Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.eligibleJobs\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"My Files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsResumeModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: resumeCount > 0 ? \"\".concat(resumeCount, \" resume\").concat(resumeCount > 1 ? 's' : '', \" uploaded\") + (lastResumeUpdate ? \" • Last updated \".concat(new Date(lastResumeUpdate).toLocaleDateString()) : '') : 'No resumes uploaded'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: resumeCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsDocumentsModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Academic certificates and marksheets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: ((profile === null || profile === void 0 ? void 0 : profile.tenth_certificate) ? 1 : 0) + ((profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate) ? 1 : 0) + semesterMarksheets.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 521,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"CURRENT ADDRESS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"District\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.district) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.state) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Pin Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.pincode) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.country) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.address) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isResumeModalOpen,\n                onClose: ()=>setIsResumeModalOpen(false),\n                resume: (profile === null || profile === void 0 ? void 0 : profile.resume_url) || (profile === null || profile === void 0 ? void 0 : profile.resume),\n                onUpload: handleResumeUpload,\n                onDelete: handleResumeDelete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 612,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentsModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isDocumentsModalOpen,\n                onClose: ()=>setIsDocumentsModalOpen(false),\n                documents: {\n                    tenth: (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate),\n                    twelfth: (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate),\n                    semesterMarksheets: semesterMarksheets\n                },\n                onUploadCertificate: _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.uploadCertificate,\n                onUploadMarksheet: _api_students__WEBPACK_IMPORTED_MODULE_5__.studentsAPI.uploadSemesterMarksheet\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"W4FX+by+W4j5qxrWRbh9767hGSg=\");\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/profile/page.jsx\n"));

/***/ })

});